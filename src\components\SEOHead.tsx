import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';

/**
 * SEOHead component for dynamically updating meta tags based on language
 */
const SEOHead: React.FC = () => {
  const { i18n } = useTranslation();

  useEffect(() => {
    // Update meta tags when language changes
    const updateMetaTags = () => {
      const isEnglish = i18n.language === 'en';
      
      // Update title
      document.title = isEnglish 
        ? 'Bulgarian Building Agency - Professional Construction Services in Bulgaria'
        : 'Българска Строителна Агенция - Професионални строителни услуги в България';
      
      // Update meta description
      const metaDescription = document.querySelector('meta[name="description"]');
      if (metaDescription) {
        metaDescription.setAttribute('content', isEnglish
          ? 'Bulgarian Building Agency - Your trusted partner in construction. Professional construction services for residential and commercial projects in Bulgaria. Free quotes from verified contractors.'
          : 'Българска Строителна Агенция - Вашият надежден партньор в строителството. Професионални строителни услуги за жилищни и търговски проекти в България. Безплатни оферти от проверени изпълнители.');
      }
      
      // Update Open Graph title
      const ogTitle = document.querySelector('meta[property="og:title"]');
      if (ogTitle) {
        ogTitle.setAttribute('content', isEnglish
          ? 'Bulgarian Building Agency - Professional Construction Services in Bulgaria'
          : 'Българска Строителна Агенция - Професионални строителни услуги в България');
      }
      
      // Update Open Graph description
      const ogDescription = document.querySelector('meta[property="og:description"]');
      if (ogDescription) {
        ogDescription.setAttribute('content', isEnglish
          ? 'Your trusted partner in construction. Professional construction services for residential and commercial projects. Get free quotes from verified contractors.'
          : 'Вашият надежден партньор в строителството. Професионални строителни услуги за жилищни и търговски проекти. Получете безплатни оферти от проверени изпълнители.');
      }
      
      // Update Open Graph locale
      const ogLocale = document.querySelector('meta[property="og:locale"]');
      if (ogLocale) {
        ogLocale.setAttribute('content', isEnglish ? 'en_US' : 'bg_BG');
      }
      
      // Update canonical and alternate links
      const canonical = document.querySelector('link[rel="canonical"]');
      if (canonical) {
        canonical.setAttribute('href', `https://building-agency.bg/${isEnglish ? 'en' : 'bg'}`);
      }
    };
    
    // Run on language change
    updateMetaTags();
    
    // Add event listener for language changes
    i18n.on('languageChanged', updateMetaTags);
    
    // Cleanup
    return () => {
      i18n.off('languageChanged', updateMetaTags);
    };
  }, [i18n]);
  
  // This component doesn't render anything
  return null;
};

export default SEOHead;
