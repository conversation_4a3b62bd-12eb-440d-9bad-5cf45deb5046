import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-scroll';
import './Hero.css';

const Hero: React.FC = () => {
  const { t } = useTranslation();
  const [currentMotoIndex, setCurrentMotoIndex] = useState(0);

  const motos = [
    t('hero.moto1'),
    t('hero.moto2'),
    t('hero.moto3')
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentMotoIndex((prevIndex) => (prevIndex + 1) % motos.length);
    }, 8000); // Match the CSS animation duration

    return () => clearInterval(interval);
  }, [motos.length]);

  return (
    <section id="hero" className="hero">
      <div className="hero-background"></div>
      <div className="hero-overlay"></div>
      <div className="hero-content">
        <img src="/images/logo.png" alt="Bulgarian Building Agency Logo" className="hero-logo" />
        <h2 className="hero-company-name">{t('navbar.brand')}</h2>
        <h1 className="hero-title">{t('hero.title')}</h1>
        <div className="moto-container">
          {motos.map((moto, index) => (
            <p
              key={index}
              className={`hero-moto ${index === currentMotoIndex ? 'active' : ''}`}
            >
              {moto}
            </p>
          ))}
        </div>
        <Link to="contact" smooth={true} duration={500} offset={-70} className="hero-cta">
          {t('hero.cta')}
        </Link>
      </div>
    </section>
  );
};

export default Hero;