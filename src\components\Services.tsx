import React from 'react';
import { useTranslation } from 'react-i18next';
import AnimatedSection from './AnimatedSection';
import './Services.css';

const Services: React.FC = () => {
  const { t } = useTranslation();

  const services = [
    {
      icon: 'fas fa-home',
      title: t('services.residential.title'),
      description: t('services.residential.description'),
    },
    {
      icon: 'fas fa-building',
      title: t('services.administrative.title'),
      description: t('services.administrative.description'),
    },
    {
      icon: 'fas fa-industry',
      title: t('services.industrial.title'),
      description: t('services.industrial.description'),
    },
    {
      icon: 'fas fa-road',
      title: t('services.infrastructure.title'),
      description: t('services.infrastructure.description'),
    },
    {
      icon: 'fas fa-users',
      title: t('services.subcontractors.title'),
      description: t('services.subcontractors.description'),
    },
  ];

  return (
    <section id="services" className="services-section">
      <div className="services-overlay"></div>
      <AnimatedSection>
        <div className="services-content">
          <h2>{t('services.title')}</h2>
          <p className="services-subtitle">{t('services.subtitle')}</p>
          <div className="services-grid">
            {services.map((service, index) => (
              <div key={index} className="service-card">
                <i className={service.icon}></i>
                <h3>{service.title}</h3>
                <p>{service.description}</p>
              </div>
            ))}
          </div>
        </div>
      </AnimatedSection>
    </section>
  );
};

export default Services;