.contact-section {
  padding: 100px 0;
  background-image: url('/images/contact-bg.jpg');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  position: relative;
}

.contact-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.95);
}

.contact-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 20px;
  text-align: center;
  position: relative;
  z-index: 1;
}

.contact-container h2 {
  font-size: 2.5rem;
  color: #2c3e50;
  margin-bottom: 1rem;
}

.contact-description {
  font-size: 1.2rem;
  color: #666;
  margin-bottom: 3rem;
}

.contact-info {
  background: white;
  padding: 2rem;
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  max-width: 500px;
  margin: 0 auto;
}

.contact-info p {
  font-size: 1.1rem;
  color: #666;
  margin: 0;
}

.contact-phones {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  width: 100%;
}

.phone-region {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  align-items: center;
}

.region-label {
  font-size: 1rem;
  font-weight: 600;
  color: #2c3e50;
}

.contact-email,
.contact-phone {
  display: inline-block;
  background-color: var(--primary-color);
  color: white;
  padding: 1rem 2.5rem;
  border: none;
  border-radius: 5px;
  font-size: 1.2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
  text-decoration: none;
}

.contact-phone {
  font-family: 'Roboto Mono', monospace;
  letter-spacing: 0.02em;
  font-weight: 500;
}

.contact-email:hover,
.contact-phone:hover {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
}

@media (max-width: 768px) {
  .contact-container h2 {
    font-size: 2rem;
  }

  .contact-description {
    font-size: 1rem;
  }

  .contact-info {
    padding: 1.5rem;
  }

  .contact-info p {
    font-size: 1rem;
  }

  .contact-phones {
    gap: 1.2rem;
  }

  .phone-region {
    gap: 0.4rem;
  }

  .region-label {
    font-size: 0.9rem;
  }

  .contact-email,
  .contact-phone {
    font-size: 1.1rem;
    padding: 0.8rem 2rem;
  }

  .contact-phone {
    font-size: 1rem;
  }
}