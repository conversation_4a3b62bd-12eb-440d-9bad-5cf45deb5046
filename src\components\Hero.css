.hero {
  position: relative;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: var(--text-light);
  overflow: hidden;
  padding-top:40px;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('/images/hero-bg.jpg');
  background-size: cover;
  background-position: center;
  z-index: -1;
  background-attachment: fixed;
  animation: parallax 20s linear infinite;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--overlay-dark);
  z-index: -1;
}

.hero-content {
  max-width: 800px;
  padding: 0 20px;
  z-index: 1;
  opacity: 0;
  animation: fadeIn 1.5s ease-out forwards;
  animation-delay: 0.5s;
  width: 100%;
}

.hero-logo {
  width: 180px;
  height: auto;
  margin-bottom: 1rem;
  animation: floatAnimation 3s ease-in-out infinite;
  filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.3));
}

.hero-company-name {
  font-size: 3rem;
  margin: 0 0 1.5rem;
  font-weight: 800;
  color: var(--text-light);
  text-shadow: 1px 1px 3px var(--shadow-dark);
  opacity: 0;
  transform: translateY(20px);
  animation: slideUpFade 0.8s ease-out forwards;
  animation-delay: 0.8s;
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
}

.hero-title {
  font-size: 1.8rem;
  margin-bottom: 1rem;
  font-weight: 700;
  text-shadow: 2px 2px 4px var(--shadow-dark);
  opacity: 0;
  transform: translateY(20px);
  animation: slideUpFade 0.8s ease-out forwards;
  animation-delay: 1s;
}

.hero-subtitle {
  font-size: 1.5rem;
  margin-bottom: 2rem;
  opacity: 0;
  transform: translateY(20px);
  animation: slideUpFade 0.8s ease-out forwards;
  animation-delay: 1.2s;
}

.hero-cta {
  display: inline-block;
  padding: 1rem 2rem;
  background-color: var(--primary-color);
  color: var(--text-light);
  text-decoration: none;
  border-radius: 5px;
  font-size: 1.2rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px var(--shadow-color);
  opacity: 0;
  transform: translateY(20px);
  animation: slideUpFade 0.8s ease-out forwards;
  animation-delay: 1.4s;
}

.hero-cta:hover {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 6px 8px var(--shadow-dark);
}

.moto-container {
  min-height: 80px; /* Increased from 60px to 80px */
  margin: 1.5rem 0;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.hero-moto {
  font-size: 1.8rem;
  color: #ffffff;
  font-weight: 500;
  margin: 0;
  position: absolute;
  width: 100%;
  text-align: center;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  opacity: 0;
  transition: opacity 1.5s ease-in-out;
  word-wrap: break-word;
  overflow-wrap: break-word;
  padding: 0 20px;
  box-sizing: border-box;
  line-height: 1.4;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.hero-moto.active {
  opacity: 1;
}

@keyframes floatAnimation {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUpFade {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInOut {
  0% {
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

@keyframes parallax {
  0% {
    background-position: center top;
  }
  100% {
    background-position: center bottom;
  }
}

@media (max-width: 768px) {
  .hero-title {
    font-size: 2.2rem;
  }

  .hero-subtitle {
    font-size: 1.2rem;
  }

  .hero-logo {
    width: 120px;
  }

  .hero-company-name {
    font-size: 1.6rem;
    margin-bottom: 1rem;
  }

  .hero-moto {
    font-size: 1.4rem;
  }

  .moto-container {
    min-height: 70px;
    margin: 1rem 0;
  }

  /* Language specific adjustments */
  :lang(bg) .hero-company-name {
    font-size: 1.4rem;
    line-height: 1.3;
  }

  :lang(bg) .hero-moto {
    font-size: 1.2rem;
    line-height: 1.3;
  }
}

/* Additional breakpoint for smaller mobile devices */
@media (max-width: 480px) {
  .hero-title {
    font-size: 1.8rem;
  }

  .hero-company-name {
    font-size: 1.4rem;
  }

  .hero-moto {
    font-size: 1.2rem;
  }

  .moto-container {
    min-height: 80px;
  }

  /* Language specific adjustments for very small screens */
  :lang(bg) .hero-company-name {
    font-size: 1.2rem;
    line-height: 1.3;
  }

  :lang(bg) .hero-moto {
    font-size: 1rem;
    line-height: 1.3;
  }

  :lang(bg) .moto-container {
    min-height: 90px;
  }
}