import React from 'react';
import { useTranslation } from 'react-i18next';
import AnimatedSection from './AnimatedSection';
import './Broker.css';

const Broker: React.FC = () => {
  const { t } = useTranslation();

  return (
    <section id="broker" className="broker-section">
      <AnimatedSection>
        <div className="broker-container">
          <h3>{t('broker.title')}</h3>
          <p className="broker-description">{t('broker.description')}</p>

          <div className="broker-options">
            <div className="broker-option">
              <h4>{t('broker.broker.title')}</h4>
              <p>{t('broker.broker.description')}</p>
              <div className="broker-contact">
                <p>{t('contact.emailInfo')}</p>
                <a href={`mailto:${t('broker.broker.contact.email')}`} className="contact-email">
                  {t('broker.broker.contact.email')}
                </a>
              </div>
            </div>

            <div className="broker-option">
              <h4>{t('broker.company.title')}</h4>
              <p>{t('broker.company.description')}</p>
              <div className="broker-contact">
                <p>{t('contact.emailInfo')}</p>
                <a href={`mailto:${t('broker.company.contact.email')}`} className="contact-email">
                  {t('broker.company.contact.email')}
                </a>
              </div>
            </div>
          </div>
        </div>
      </AnimatedSection>
    </section>
  );
};

export default Broker;