.terms-content {
  color: var(--text-primary);
  line-height: 1.6;
  padding-bottom: 1rem;
}

.terms-section {
  margin-bottom: 2rem;
}

.terms-section:last-of-type {
  margin-bottom: 1rem;
}

.terms-section-title {
  font-size: 1.1rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 0.75rem;
  font-family: 'Montserrat', sans-serif;
}

.terms-section-content {
  margin-left: 0;
}

.terms-paragraph {
  margin-bottom: 0.75rem;
  text-align: justify;
  font-size: 0.95rem;
}

.terms-paragraph:last-child {
  margin-bottom: 0;
}

.terms-footer {
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e5e7eb;
  text-align: center;
}

.terms-close-button {
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 12px 32px;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: '<PERSON><PERSON><PERSON>', sans-serif;
  letter-spacing: 0.02em;
}

.terms-close-button:hover {
  background-color: var(--accent-dark);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.terms-close-button:active {
  transform: translateY(0);
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .terms-section-title {
    font-size: 1rem;
  }
  
  .terms-paragraph {
    font-size: 0.9rem;
    text-align: left;
  }
  
  .terms-close-button {
    padding: 10px 24px;
    font-size: 0.9rem;
  }
}
