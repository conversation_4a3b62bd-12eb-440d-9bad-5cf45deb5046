import { useTranslation } from 'react-i18next';
import AnimatedSection from './AnimatedSection';
import './HowItWorks.css';

const HowItWorks = () => {
  const { t } = useTranslation();

  const steps = [
    { key: 'freeInquiry', number: '1', icon: '/images/step1.jpg' },
    { key: 'networkDistribution', number: '2', icon: '/images/step2.jpg' },
    { key: 'bestOffers', number: '3', icon: '/images/step3.jpg' }
  ];

  return (
    <section id="how-it-works" className="how-it-works-section">
      <AnimatedSection>
        <div className="how-it-works-container">
          <div className="section-title">
            <h2>{t('howItWorks.title')}</h2>
            <img src="/images/howitworks.svg" alt="Construction process gear icon" className="rotating-gear" />
          </div>

          <div className="how-it-works-illustration">
            <img src="/images/howitworks2.jpg" alt="Building construction process diagram" className="process-illustration" />
          </div>

          <div className="steps-timeline">
            {steps.map((step) => (
              <div key={step.key} className="step-item">
                <div className="step-number">{step.number}</div>
                <div className="step-content">
                  <h3>{t(`howItWorks.steps.${step.key}.title`)}</h3>
                  <p>{t(`howItWorks.steps.${step.key}.description`)}</p>
                </div>
                <div className="step-icon">
                  <img src={step.icon} alt={t(`howItWorks.steps.${step.key}.title`) + ' - Step ' + step.number} />
                </div>
                {step.number !== '3' && <div className="step-connector"></div>}
              </div>
            ))}
          </div>
        </div>
      </AnimatedSection>
    </section>
  );
};

export default HowItWorks;