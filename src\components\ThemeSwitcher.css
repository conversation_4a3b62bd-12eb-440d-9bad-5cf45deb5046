.theme-switcher {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: var(--bg-primary);
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 10px var(--shadow-color);
  z-index: 1000;
}

.theme-switcher h3 {
  margin: 0 0 10px 0;
  color: var(--text-primary);
  font-size: 14px;
}

.theme-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.theme-button {
  padding: 8px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  color: var(--text-light);
  transition: opacity 0.2s;
}

.theme-button:hover {
  opacity: 0.9;
}

/* Theme-specific button colors */
.theme-blue {
  background-color: var(--primary-color);
}

.theme-green {
  background-color: var(--primary-color);
}

.theme-purple {
  background-color: var(--primary-color);
}

.theme-orange {
  background-color: var(--primary-color);
}

.theme-red {
  background-color: var(--primary-color);
} 