.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: transparent;
  transition: all 0.3s ease;
  padding: 1rem 0;
}

.navbar.scrolled {
  background: var(--overlay-dark);
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 10px var(--shadow-color);
}

.navbar-container {
  margin: 0 40px;
  padding: 0 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.navbar-left {
  display: flex;
  align-items: center;
}

.navbar-left-links {
  display: flex;
  gap: 1.5rem;
  align-items: center;
  margin-left: 2rem;
}

.mobile-only {
  display: none;
}

.navbar-right {
  display: flex;
  align-items: center;
}

.navbar-brand {
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: opacity 0.2s;
}

.navbar-brand:hover {
  opacity: 0.8;
}

.navbar-logo {
  height: 40px;
  width: auto;
  margin-right: 10px;
}

.navbar-name {
  font-size: 1.5rem;
  font-weight: bold;
  color: var(--text-light);
}

.navbar-links {
  display: flex;
  gap: 1.5rem;
  align-items: center;
}

.nav-link {
  color: var(--text-light);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
  cursor: pointer;
  font-size: 16px;
}

.nav-link:hover {
  color: var(--primary-light);
}

.navbar-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.language-toggle {
  background: transparent;
  border: 2px solid #3498db;
  color: #3498db;
  padding: 0.5rem 1rem;
  border-radius: 5px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
}

.language-toggle:hover {
  background: #3498db;
  color: white;
}

.language-switch {
  background: transparent;
  border: 2px solid var(--text-light);
  color: var(--text-light);
  padding: 0.5rem 1rem;
  border-radius: 5px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  margin-left: 1rem;
  font-size: 16px;
}

.language-switch:hover {
  background: var(--text-light);
  color: var(--primary-color);
}

.hamburger {
  display: none;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 5px;
  cursor: pointer;
  padding: 10px;
  border-radius: 4px;
  transition: all 0.3s ease;
  position: relative;
  z-index: 1001;
  width: 40px;
  height: 40px;
  background-color: rgba(0, 0, 0, 0.1);
}

.hamburger:hover {
  background-color: rgba(0, 0, 0, 0.15);
}

.hamburger span {
  display: block;
  width: 24px;
  height: 2px;
  background-color: var(--text-light);
  transition: all 0.3s ease;
  border-radius: 1px;
}

.cta-button {
  background-color: var(--primary-color);
  color: var(--text-light);
  border: none;
  padding: 0.5rem 1.5rem;
  border-radius: 5px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  text-decoration: none;
  font-size: 16px;
}

.cta-button:hover {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
}

@media (max-width: 768px) {
  .hamburger {
    display: flex;
  }

  .navbar-container {
    padding: 0 15px;
  }

  .navbar-left {
    flex: 1;
  }

  .navbar-left-links {
    display: none;
  }

  .mobile-only {
    display: block;
  }

  .navbar-links {
    position: fixed;
    top: 70px;
    left: 0;
    right: 0;
    background: var(--overlay-dark);
    flex-direction: column;
    padding: 2rem;
    gap: 1.5rem;
    transform: translateY(-100%);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px var(--shadow-color);
    backdrop-filter: blur(10px);
  }

  .navbar-links.active {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
  }

  .navbar-links a {
    font-size: 1.2rem;
    color: var(--text-light);
  }

  .navbar-links .nav-link:hover {
    color: var(--primary-light);
  }

  .navbar-links .cta-button {
    background-color: var(--primary-color);
    color: var(--text-light);
    text-align: center;
    width: 100%;
  }

  .navbar-links .cta-button:hover {
    background-color: var(--primary-dark);
  }

  .hamburger.active {
    background-color: var(--primary-color);
    box-shadow: 0 0 8px rgba(0, 0, 0, 0.3);
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .hamburger.active span {
    background-color: white;
    height: 2px;
    width: 22px;
    margin: 0 auto;
  }

  .hamburger.active span:nth-child(1) {
    position: absolute;
    transform: rotate(45deg);
  }

  .hamburger.active span:nth-child(2) {
    opacity: 0;
  }

  .hamburger.active span:nth-child(3) {
    position: absolute;
    transform: rotate(-45deg);
  }

  .language-switch {
    margin: 1rem 0;
    width: 100%;
    background: transparent;
    border: 2px solid var(--text-light);
    color: var(--text-light);
  }

  .language-switch:hover {
    background: var(--text-light);
    color: var(--primary-color);
  }
}