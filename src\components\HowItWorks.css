.how-it-works-section {
  padding: 100px 0;
  background-image: url('/images/how-it-works-bg.png');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  position: relative;
}

.how-it-works-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.75);
}

.how-it-works-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  text-align: center;
  position: relative;
  z-index: 1;
}

.section-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 3rem;
}

.section-title h2 {
  font-size: 2.5rem;
  color: #2c3e50;
  margin: 0;
}

.rotating-gear {
  width: 40px;
  height: 40px;
  animation: rotate 8s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.how-it-works-illustration {
  margin: 2rem auto 3rem;
  max-width: 600px;
  text-align: center;
}

.process-illustration {
  width: 100%;
  max-width: 600px;
  height: auto;
  border-radius: 10px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  transition: transform 0.3s ease;
}

.process-illustration:hover {
  transform: scale(1.02);
}

.steps-timeline {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4rem;
  position: relative;
  padding: 2rem 0;
}

.step-item {
  display: flex;
  align-items: center;
  width: 100%;
  max-width: 800px;
  position: relative;
  background: white;
  padding: 2rem;
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(210, 78, 68, 0.1);
  overflow: hidden;
}

.step-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(225, 58, 46, 0.15), rgba(210, 78, 68, 0));
  opacity: 0;
  transition: opacity 0.4s ease;
  z-index: 1; /* Ensure it is above the image */
}

.step-item:hover {
  transform: translateY(-12px) scale(1.02);
  box-shadow: 0 20px 40px rgba(210, 78, 68, 0.12);
  border-color: rgba(210, 78, 68, 0.15);
}

.step-item:hover::before {
  opacity: 1;
}

.step-item:last-child {
  margin-bottom: 0;
}

.step-number {
  width: 60px;
  height: 60px;
  background-color: var(--primary-color);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 1.5rem;
  margin-right: 2rem;
  flex-shrink: 0;
  position: relative;
  z-index: 2;
  animation: bounce 2s infinite;
}

.step-icon {
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.step-icon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  transition: transform 0.3s ease;
  position: relative;
  z-index: 0; /* Ensure it is below the hover overlay */
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

.step-item:hover .step-icon img {
  transform: scale(1.1);
}

.step-content {
  text-align: left;
  flex-grow: 1;
  margin-right: 2rem;
}

.step-content h3 {
  color: #2c3e50;
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

.step-content p {
  color: #666;
  font-size: 1.1rem;
  line-height: 1.6;
}

.step-connector {
  position: absolute;
  left: 30px;
  top: 100%;
  bottom: -4rem;
  width: 2px;
  background-color: var(--primary-color);
  opacity: 0;
  animation: fadeIn 1s ease-in-out forwards;
  animation-delay: 0.5s;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.step-item:last-child .step-connector {
  display: none;
}

@media (max-width: 768px) {
  .section-title h2 {
    font-size: 2rem;
  }

  .rotating-gear {
    width: 32px;
    height: 32px;
  }

  .how-it-works-container h2 {
    font-size: 2rem;
  }

  .how-it-works-illustration {
    margin: 1.5rem auto 2rem;
  }

  .process-illustration {
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
  }

  .step-content h3 {
    font-size: 1.3rem;
  }

  .step-content p {
    font-size: 1rem;
  }

  .step-number {
    width: 50px;
    height: 50px;
    font-size: 1.2rem;
    margin-right: 1.5rem;
  }

  .step-content {
    margin-right: 1.5rem;
  }

  .step-icon {
    width: 60px;
    height: 60px;
  }

  .step-item {
    padding: 1.5rem;
  }

  .step-connector {
    left: 25px;
    bottom: -3rem;
  }
}