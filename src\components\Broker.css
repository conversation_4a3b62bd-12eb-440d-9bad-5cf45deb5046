.broker-section {
  padding: 100px 0;
  position: relative;
  color: var(--text-light);
  overflow: hidden;
}

.broker-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('/images/broker-bg.jpg');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  z-index: -2;
}

.broker-section::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--overlay-dark);
  z-index: -1;
}

.broker-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  text-align: center;
  position: relative;
  z-index: 1;
}

.broker-container h3 {
  font-size: 2.5rem;
  color: var(--text-light);
  margin-bottom: 1.5rem;
  text-shadow: 2px 2px 4px var(--shadow-dark);
}

.broker-description {
  font-size: 1.2rem;
  color: var(--text-light);
  margin-bottom: 3rem;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
  text-shadow: 1px 1px 2px var(--shadow-dark);
}

.broker-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.broker-option {
  background: rgba(255, 255, 255, 0.1);
  padding: 2rem;
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(5px);
}

.broker-option:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
  background: rgba(255, 255, 255, 0.15);
}

.broker-option h4 {
  font-size: 1.5rem;
  color: var(--text-light);
  margin-bottom: 1rem;
  text-shadow: 1px 1px 2px var(--shadow-dark);
}

.broker-option p {
  color: var(--text-light);
  margin-bottom: 1.5rem;
  line-height: 1.6;
  text-shadow: 1px 1px 2px var(--shadow-dark);
}

.broker-contact {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

.contact-info {
  background: rgba(255, 255, 255, 0.1);
  padding: 1.5rem;
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  gap: 1rem;
  max-width: 400px;
  margin: 0 auto;
  backdrop-filter: blur(5px);
}

.contact-info p {
  font-size: 1.1rem;
  color: #666;
  margin: 0;
  text-shadow: none;
}

.contact-email {
  display: inline-block;
  background-color: var(--primary-color);
  color: var(--text-light);
  padding: 1rem 2.5rem;
  border: none;
  border-radius: 5px;
  font-size: 1.2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
  text-decoration: none;
  word-break: break-word;
  max-width: 100%;
  box-sizing: border-box;
}

.contact-email:hover {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 6px 8px var(--shadow-dark);
}

@media (max-width: 768px) {
  .broker-container h3 {
    font-size: 2rem;
  }

  .broker-description {
    font-size: 1.1rem;
  }

  .broker-option {
    padding: 1.5rem;
  }

  .broker-option h4 {
    font-size: 1.3rem;
  }

  .contact-info {
    padding: 1rem;
  }

  .contact-info p {
    font-size: 1rem;
  }

  .contact-email {
    font-size: 1.1rem;
    padding: 0.8rem 1.5rem;
    width: 100%;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .broker-option {
    padding: 1.25rem;
  }

  .contact-email {
    font-size: 1rem;
    padding: 0.7rem 1rem;
    white-space: normal;
  }
}