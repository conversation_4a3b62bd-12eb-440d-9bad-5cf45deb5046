#root {
  font-family: '<PERSON><PERSON><PERSON>', sans-serif;
  letter-spacing: 0.02em;
  line-height: 1.6;
  opacity: 0;
  animation: fadeInApp 1.5s ease-out forwards;
}

h1, h2, h3, h4, h5, h6, .hero-title, .hero-subtitle, .section-title h2, .service-card h3 {
  font-family: 'Montserrat', sans-serif;
  font-weight: 700;
  letter-spacing: -0.01em;
}

.hero-title {
  letter-spacing: -0.03em;
  font-weight: 800;
}

.service-card h3 {
  letter-spacing: -0.02em;
}

.hero-subtitle, .hero-moto {
  font-family: 'Raleway', sans-serif;
  font-weight: 500;
}

.hero-cta, button, .btn, .contact-phone {
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
  letter-spacing: 0.05em;
}

/* Navbar specific styles */
.navbar-links .nav-link, .navbar-links .cta-button, .language-switch {
  font-family: '<PERSON><PERSON><PERSON>', sans-serif;
  font-weight: 600;
  letter-spacing: 0;
  font-size: 0.95rem;
}

/* For Bulgarian language */
:lang(bg) .navbar-links .nav-link,
:lang(bg) .navbar-links .cta-button,
:lang(bg) .language-switch {
  font-family: 'Montserrat', sans-serif;
  font-size: 0.9rem;
  letter-spacing: -0.03em;
  font-weight: 500;
}

@keyframes fadeInApp {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
