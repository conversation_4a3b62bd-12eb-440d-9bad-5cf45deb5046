import { useEffect } from 'react';
import "./App.css";
import Navbar from "./components/Navbar";
import Hero from "./components/Hero";
import Services from "./components/Services";
import HowItWorks from "./components/HowItWorks";
import Benefits from "./components/Benefits";
import Contact from "./components/Contact";
import Broker from "./components/Broker";
import SEOHead from "./components/SEOHead";
import Footer from "./components/Footer";
import './i18n/i18n';
import './styles/theme.css';
import './styles/themes.css';

function App() {
  useEffect(() => {
    // Apply default theme
    document.body.classList.add('theme-orange');
  }, []);

  useEffect(() => {
    const handleLoad = () => {
      setTimeout(() => {
        window.scrollTo(0, 0);
        document.documentElement.scrollTop = 0;
        document.body.scrollTop = 0;
      }, 100);
    };

    window.addEventListener('load', handleLoad);
    handleLoad(); // Also run on initial mount

    return () => {
      window.removeEventListener('load', handleLoad);
    };
  }, []);

  return (
    <div className="App">
      <SEOHead />
      <Navbar />
      <Hero />
      <Services />
      <HowItWorks />
      <Benefits />
      <Contact />
      <Broker />
      <Footer />
    </div>
  );
}

export default App;
