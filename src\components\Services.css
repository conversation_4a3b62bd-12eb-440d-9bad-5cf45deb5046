.services-section {
  position: relative;
  padding: 120px 0;
  background: url('/images/services.webp') no-repeat center center;
  background-size: cover;
  color: var(--text-primary);
  text-align: center;
  overflow: hidden;
}

@media (min-width: 1500px) {
  .services-section {
    animation: parallax-scale2 12s linear infinite;
  }

  @keyframes parallax-scale2 {
    0% {
      background-size: 130%;
    }
    100% {
      background-size: 105%;
    }
  }
}

.services-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.75) 50%, rgba(255, 255, 255, 0.95) 100%);
  z-index: 0;
}

.services-content {
  position: relative;
  z-index: 2;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 24px;
}

.services-content h2 {
  font-size: 3.5rem;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
  font-weight: 800;
  letter-spacing: -0.02em;
  position: relative;
  z-index: 2;
}

.services-subtitle {
  font-size: 1.25rem;
  margin-bottom: 4rem;
  color: var(--text-secondary);
  opacity: 0.9;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
  position: relative;
  z-index: 2;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 1.5rem;
  justify-items: center;
  max-width: 1400px;
  margin: 0 auto;
  position: relative;
  z-index: 2;
}

.service-card {
  background: rgba(255, 255, 255, 0.9);
  padding: 2rem 1.5rem;
  border-radius: 24px;
  text-align: center;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  max-width: 260px;
  width: 100%;
  box-shadow: 0 8px 32px rgba(210, 78, 68, 0.08);
  backdrop-filter: blur(10px);
  position: relative;
  z-index: 2;
  border: 1px solid rgba(210, 78, 68, 0.1);
  overflow: hidden;
}

.service-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(210, 78, 68, 0.05), rgba(210, 78, 68, 0));
  opacity: 0;
  transition: opacity 0.4s ease;
}

.service-card:hover {
  transform: translateY(-12px) scale(1.05) rotate(2deg);
  box-shadow: 0 20px 40px rgba(210, 78, 68, 0.2), 0 0 15px var(--primary-color);
  background: color-mix(in srgb, var(--primary-color) 15%, transparent);
  border-color: rgba(210, 78, 68, 0.15);
  z-index: 3;
}

.service-card:hover::before {
  opacity: 1;
}

.service-card i {
  font-size: 3rem;
  margin-bottom: 1.5rem;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  background: linear-gradient(135deg, #d24e44, #e85d53);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.service-card:hover i {
  transform: scale(1.1) rotate(5deg);
}

.service-card h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: var(--text-primary);
  font-weight: 700;
  position: relative;
  letter-spacing: -0.01em;
}

.service-card p {
  color: var(--text-secondary);
  line-height: 1.5;
  font-weight: 400;
  margin: 0;
  font-size: 1rem;
}

@media (max-width: 1200px) {
  .services-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
  }

  .service-card {
    max-width: 300px;
  }
}

@media (max-width: 768px) {
  .services-section {
    padding: 80px 0;
  }

  .services-content h2 {
    font-size: 2.75rem;
  }

  .services-subtitle {
    font-size: 1.1rem;
    margin-bottom: 3rem;
  }

  .services-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .service-card {
    padding: 2rem 1.25rem;
    max-width: 100%;
  }

  .service-card i {
    font-size: 2.75rem;
  }

  .service-card h3 {
    font-size: 1.4rem;
  }

  .service-card p {
    font-size: 0.95rem;
  }
}

@media (max-width: 480px) {
  .services-section {
    padding: 60px 0;
  }

  .services-content h2 {
    font-size: 2.25rem;
  }

  .services-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .service-card {
    padding: 2rem 1.25rem;
    max-width: 100%;
  }
}