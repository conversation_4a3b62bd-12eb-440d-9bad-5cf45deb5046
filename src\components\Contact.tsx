import { useTranslation } from 'react-i18next';
import AnimatedSection from './AnimatedSection';
import './Contact.css';

const Contact = () => {
  const { t } = useTranslation();

  return (
    <section id="contact" className="contact-section">
      <AnimatedSection>
        <div className="contact-container">
          <h2>{t('contact.title')}</h2>
          <p className="contact-description">{t('contact.description')}</p>

          <div className="contact-info">
            <p>{t('contact.emailInfo', 'You can reach us at:')}</p>
            <a href="mailto:<EMAIL>" className="contact-email">
              <EMAIL>
            </a>
            <p>{t('contact.phoneInfo', 'Or call us at:')}</p>
            <div className="contact-phones">
              <div className="phone-region">
                <span className="region-label">{t('contact.westernRegion', 'София и Западна България')}</span>
                <a href="tel:0883555316" className="contact-phone">
                  0883 555 316
                </a>
              </div>
              <div className="phone-region">
                <span className="region-label">{t('contact.easternRegion', 'Централна и Източна България')}</span>
                <a href="tel:00899129179" className="contact-phone">
                  0899 129 179
                </a>
              </div>
            </div>
          </div>
        </div>
      </AnimatedSection>
    </section>
  );
};

export default Contact;