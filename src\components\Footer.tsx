import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import TermsAndConditions from './TermsAndConditions';
import './Footer.css';

const Footer: React.FC = () => {
  const { t } = useTranslation();
  const [isTermsOpen, setIsTermsOpen] = useState(false);

  const openTerms = () => setIsTermsOpen(true);
  const closeTerms = () => setIsTermsOpen(false);

  return (
    <>
      <footer className="footer">
        <div className="footer-content">
          <p>{t('footer.copyright')}</p>
          <button
            className="footer-link"
            onClick={openTerms}
            aria-label={t('footer.termsAndConditions')}
          >
            {t('footer.termsAndConditions')}
          </button>
        </div>
      </footer>

      <TermsAndConditions
        isOpen={isTermsOpen}
        onClose={closeTerms}
      />
    </>
  );
};

export default Footer;
