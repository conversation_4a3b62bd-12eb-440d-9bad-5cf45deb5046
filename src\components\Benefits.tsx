import { useTranslation } from 'react-i18next';
import AnimatedSection from './AnimatedSection';
import './Benefits.css';

const Benefits = () => {
  const { t } = useTranslation();

  const benefits = [
    { key: 'freeService', icon: '/images/free.jpg' },
    { key: 'wideNetwork', icon: '/images/networking.jpg' },
    { key: 'professionalConsultation', icon: '/images/professional.jpg' }
  ];

  return (
    <section id="benefits" className="benefits-section">
      <AnimatedSection>
        <div className="benefits-container">
          <h2>{t('benefits.title')}</h2>
          <p className="benefits-subtitle">{t('benefits.subtitle')}</p>
          
          <div className="benefits-grid">
            {benefits.map((benefit) => (
              <div key={benefit.key} className="benefit-card">
                <div className="benefit-icon">
                  <img src={benefit.icon} alt={t(`benefits.items.${benefit.key}.title`)} />
                </div>
                <h3>{t(`benefits.items.${benefit.key}.title`)}</h3>
                <p>{t(`benefits.items.${benefit.key}.description`)}</p>
              </div>
            ))}
          </div>
        </div>
      </AnimatedSection>
    </section>
  );
};

export default Benefits; 