{"name": "building-agency", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@fortawesome/free-solid-svg-icons": "^6.6.0", "@fortawesome/react-fontawesome": "^0.2.2", "@types/react-scroll": "^1.8.10", "i18next": "^24.2.3", "i18next-browser-languagedetector": "^8.0.4", "react": "^18.3.1", "react-dom": "^18.3.1", "react-i18next": "^15.4.1", "react-scroll": "^1.9.3"}, "devDependencies": {"@eslint/js": "^9.11.1", "@types/react": "^18.3.10", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.2", "eslint": "^9.11.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.12", "globals": "^15.9.0", "typescript": "^5.5.3", "typescript-eslint": "^8.7.0", "vite": "^5.4.8"}}