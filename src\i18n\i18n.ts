import i18n from "i18next";
import { initReactI18next } from "react-i18next";
import LanguageDetector from "i18next-browser-languagedetector";
import enTranslations from "./locales/en.json";
import bgTranslations from "./locales/bg.json";

i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources: {
      en: {
        translation: enTranslations,
      },
      bg: {
        translation: bgTranslations,
      },
    },
    fallbackLng: "en",
    interpolation: {
      escapeValue: false,
    },
  });

// Set the lang attribute on the HTML element when language changes
i18n.on("languageChanged", (lng) => {
  document.documentElement.lang = lng;
});

// Set initial language attribute
document.documentElement.lang = i18n.language;

export default i18n;
