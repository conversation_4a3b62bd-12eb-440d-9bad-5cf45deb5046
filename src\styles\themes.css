/* Modern Blue Theme */
.theme-blue {
  --primary-color: #2c3e50;
  --primary-dark: #1a252f;
  --primary-light: #34495e;
  --secondary-color: #3498db;
  --secondary-dark: #2980b9;
  --secondary-light: #5dade2;
  --accent-color: #e74c3c;
  --accent-dark: #c0392b;
  --accent-light: #ff6b6b;
}

/* Professional Green Theme */
.theme-green {
  --primary-color: #2ecc71;
  --primary-dark: #27ae60;
  --primary-light: #2ecc71;
  --secondary-color: #2c3e50;
  --secondary-dark: #1a252f;
  --secondary-light: #34495e;
  --accent-color: #e74c3c;
  --accent-dark: #c0392b;
  --accent-light: #ff6b6b;
}

/* Elegant Purple Theme */
.theme-purple {
  --primary-color: #9b59b6;
  --primary-dark: #8e44ad;
  --primary-light: #a569bd;
  --secondary-color: #2c3e50;
  --secondary-dark: #1a252f;
  --secondary-light: #34495e;
  --accent-color: #e74c3c;
  --accent-dark: #c0392b;
  --accent-light: #ff6b6b;
}

/* Corporate Orange Theme */
.theme-orange {
  --primary-color: #e6960e;
  --primary-dark: #db690b;
  --primary-light: #f1c40f;
  --secondary-color: #2c3e50;
  --secondary-dark: #1a252f;
  --secondary-light: #34495e;
  --accent-color: #e74c3c;
  --accent-dark: #c0392b;
  --accent-light: #ff6b6b;
}

/* Classic Red Theme */
.theme-red {
  --primary-color: #e74c3c;
  --primary-dark: #c0392b;
  --primary-light: #ff6b6b;
  --secondary-color: #2c3e50;
  --secondary-dark: #1a252f;
  --secondary-light: #34495e;
  --accent-color: #3498db;
  --accent-dark: #2980b9;
  --accent-light: #5dade2;
} 