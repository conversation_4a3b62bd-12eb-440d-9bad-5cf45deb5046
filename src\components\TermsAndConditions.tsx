import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import Modal from './Modal';
import './TermsAndConditions.css';

interface TermsAndConditionsProps {
  isOpen: boolean;
  onClose: () => void;
}

const TermsAndConditions: React.FC<TermsAndConditionsProps> = ({ isOpen, onClose }) => {
  const { t } = useTranslation();

  const sections = [
    'section1',
    'section2', 
    'section3',
    'section4',
    'section5',
    'section6',
    'section7'
  ];

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={t('termsAndConditions.title')}
    >
      <div className="terms-content">
        {sections.map((section) => (
          <div key={section} className="terms-section">
            <h3 className="terms-section-title">
              {t(`termsAndConditions.${section}.title`)}
            </h3>
            <div className="terms-section-content">
              {t(`termsAndConditions.${section}.content`).split('\n\n').map((paragraph, index) => (
                <p key={index} className="terms-paragraph">
                  {paragraph}
                </p>
              ))}
            </div>
          </div>
        ))}
        
        <div className="terms-footer">
          <button className="terms-close-button" onClick={onClose}>
            {t('termsAndConditions.close')}
          </button>
        </div>
      </div>
    </Modal>
  );
};

export default TermsAndConditions;
