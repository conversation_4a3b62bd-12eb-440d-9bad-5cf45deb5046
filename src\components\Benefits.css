.benefits-section {
  padding: 100px 0;
  background-image: url('/images/benefits-bg.jpg');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  position: relative;
}

.benefits-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(248, 249, 250, 0.95);
}

.benefits-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  text-align: center;
  position: relative;
  z-index: 1;
}

.benefits-container h2 {
  font-size: 2.5rem;
  color: #2c3e50;
  margin-bottom: 3rem;
}

.benefits-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.benefit-card {
  background: white;
  padding: 2rem;
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(5px);
  position: relative;
  border: 1px solid rgba(210, 78, 68, 0.1);
  overflow: hidden;
}

.benefit-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(225, 58, 46, 0.15), rgba(210, 78, 68, 0));
  opacity: 0;
  transition: opacity 0.4s ease;
}

.benefit-card:hover {
  transform: translateY(-12px) scale(1.02);
  box-shadow: 0 20px 40px rgba(210, 78, 68, 0.12);
  border-color: rgba(210, 78, 68, 0.15);
}

.benefit-card:hover::before {
  opacity: 1;
}

.benefit-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.benefit-icon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.benefit-card h3 {
  color: #2c3e50;
  font-size: 1.5rem;
  margin: 0;
}

@media (max-width: 768px) {
  .benefits-container h2 {
    font-size: 2rem;
  }
  
  .benefit-card {
    padding: 1.5rem;
  }
  
  .benefit-icon {
    width: 60px;
    height: 60px;
  }
  
  .benefit-card h3 {
    font-size: 1.2rem;
  }
} 