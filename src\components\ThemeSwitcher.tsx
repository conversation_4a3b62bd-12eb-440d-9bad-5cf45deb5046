import React from 'react';
import './ThemeSwitcher.css';

const ThemeSwitcher: React.FC = () => {
  const themes = ['blue', 'green', 'purple', 'orange', 'red'];

  const changeTheme = (theme: string) => {
    // Remove all theme classes from body
    themes.forEach(t => document.body.classList.remove(`theme-${t}`));
    // Add the selected theme class
    document.body.classList.add(`theme-${theme}`);
  };

  return (
    <div className="theme-switcher">
      <h3>Color Theme</h3>
      <div className="theme-buttons">
        {themes.map(theme => (
          <button
            key={theme}
            onClick={() => changeTheme(theme)}
            className={`theme-button theme-${theme}`}
          >
            {theme.charAt(0).toUpperCase() + theme.slice(1)}
          </button>
        ))}
      </div>
    </div>
  );
};

export default ThemeSwitcher; 