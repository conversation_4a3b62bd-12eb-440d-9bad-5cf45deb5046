import React, { useState, useEffect } from 'react';
import { Link } from 'react-scroll';
import { useTranslation } from 'react-i18next';
import './Navbar.css';

const Navbar: React.FC = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const { t, i18n } = useTranslation();

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
    setIsMenuOpen(false);
  };

  const toggleLanguage = () => {
    const newLang = i18n.language === 'en' ? 'bg' : 'en';
    i18n.changeLanguage(newLang);
  };

  const handleLinkClick = () => {
    setIsMenuOpen(false);
  };

  return (
    <nav className={`navbar ${isScrolled ? 'scrolled' : ''}`}>
      <div className="navbar-container">
        <div className="navbar-left">
          <div className="navbar-brand" onClick={scrollToTop}>
            <img src="/images/logo.png" alt="Bulgarian Building Agency Logo" className="navbar-logo" />
          </div>

          <div className="navbar-left-links">
            <Link to="services" smooth={true} duration={500} offset={-70} className="nav-link" onClick={handleLinkClick}>
              {t('navbar.services')}
            </Link>
            <Link to="how-it-works" smooth={true} duration={500} offset={-70} className="nav-link" onClick={handleLinkClick}>
              {t('howItWorks.title')}
            </Link>
            <Link to="benefits" smooth={true} duration={500} offset={-70} className="nav-link" onClick={handleLinkClick}>
              {t('benefits.title')}
            </Link>
          </div>
        </div>

        <div className="navbar-right">
          <div className={`navbar-links ${isMenuOpen ? 'active' : ''}`}>
            {/* Mobile-only links that are duplicated from the left side */}
            <Link to="services" smooth={true} duration={500} offset={-70} className="nav-link mobile-only" onClick={handleLinkClick}>
              {t('navbar.services')}
            </Link>
            <Link to="how-it-works" smooth={true} duration={500} offset={-70} className="nav-link mobile-only" onClick={handleLinkClick}>
              {t('howItWorks.title')}
            </Link>
            <Link to="benefits" smooth={true} duration={500} offset={-70} className="nav-link mobile-only" onClick={handleLinkClick}>
              {t('benefits.title')}
            </Link>
            {/* Right side links */}
            <Link to="contact" smooth={true} duration={500} offset={-70} className="cta-button" onClick={handleLinkClick}>
              {t('navbar.cta')}
            </Link>
            <Link to="broker" smooth={true} duration={500} offset={-70} className="nav-link" onClick={handleLinkClick}>
              {t('navbar.broker')}
            </Link>
            <button onClick={toggleLanguage} className="language-switch">
              {i18n.language === 'en' ? 'BG' : 'EN'}
            </button>
          </div>

          <button
            className={`hamburger ${isMenuOpen ? 'active' : ''}`}
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            <span></span>
            <span></span>
            <span></span>
          </button>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;